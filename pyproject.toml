[project]
name = "nsfw"
version = "0.1.0"
description = ""
authors = [
    {name = "<PERSON>", email = "<EMAIL>"},
]
readme = "README.md"
requires-python = ">=3.12.4,<4.0.0"

[tool.poetry.dependencies]
nudenet = "^3.4.2"
textual = "^5.3.0"
pyperclip = "^1.8.2"

[tool.poetry.group.dev.dependencies]
black = "^25.1.0"
isort = "^6.0.1"
mypy = "^1.17.1"
pylint = "^3.3.7"
textual-dev = "^1.7.0"

[tool.black]
line_length = 120

[tool.isort] # see: https://pycqa.github.io/isort/docs/configuration/options.html
force_single_line = true # force one import per line
include_trailing_comma = false
known_third_party=""
line_length = 120
import_heading_future="Future libraries"
import_heading_stdlib="Standard libraries"
import_heading_thirdparty="Third party libraries"
import_heading_firstparty="Project libraries"
import_heading_localfolder="Local folder libraries"
multi_line_output = 7
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
