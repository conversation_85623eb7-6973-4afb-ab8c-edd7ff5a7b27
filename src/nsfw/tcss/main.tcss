Screen {
    background: #282a36;
    color: #f8f8f2;
    border: solid darkslateblue;
}

#results-container {
    width: 100%;
    height: 100%;
    padding: 1;
    layout: grid;
    grid-rows: 2 4 2 5fr 2fr;
}

#results-widget {
    height: 2fr;
    border: solid darkslateblue;
    padding-left: 1;
    padding-right: 1;
}

#notifications-widget {
    height: 1fr;
    border: solid darkslateblue;
    padding-left: 1;
    padding-right: 1;
}

But<PERSON> {
    color: #f8f8f2;
    margin-bottom: 1;
}

#scan-buttons Button {
    width: 50%;
    margin: 0 2 0 1;
}

#scan-directory {
    padding-left: 1;
}


#buttons-container {
    width: 100%;
    height: auto; /* Use auto-height to accommodate content */
    background: #21222c;
    border: solid darkslateblue;
    align: center middle; /* Center the content within the container */
}

.centered-label {
    text-align: center;
}

.checkbox-row {
    width: 100%;
    height: auto;
    margin-bottom: 1;
}

Button:hover {
    background: #44475a;
}

Button.-primary {
    background: #50fa7b;
    color: #282a36; /* Ensure text is dark on green background */
}

Button.-primary:hover {
    background: #44c76a;
}

#stop {
    background: #FF5555;
    color: #f8f8f2;
}

#stop:hover {
    background: #bd3737;
}

Header {
    background: #21222c;
    color: #f8f8f2;
}

Footer {
    background: #21222c;
    color: #f8f8f2;
}

#stats-line {
    height: 5;
    layout: horizontal;
    padding-left: 1;
    padding-right: 1;
    width: 100%;
}

#scan-count {
    width: 50%;
}

#scan-timer {
    text-align: right;
    width: 50%;
}

/**********************************************************************************************************************/
/* Settings page css */
/**********************************************************************************************************************/

/* Styling for the buttons container (fixed at bottom) */
#buttons-container {
    width: 100%;
    height: auto; /* Auto height to fit content */
    background: #21222c;
    border: solid darkslateblue;
}

.button-row {
    width: 100%;
    height: auto;
    margin: 1 0;
    align: center middle; /* Center the row vertically and horizontally */
}

.button-row Button {
    width: 23%; /* Revert to percentage width, adjust as needed for screen size */
    margin: 0 1 0 0; /* Add 1 character right margin for spacing */
    min-height: 3;
}

/* Ensure the last button in the row doesn't have a right margin */
.button-row Button:last-child {
    margin-right: 0;
}

/* Adjust checkbox width based on how many per row */
Checkbox {
    width: 25%;
    height: 3;
    max-width: 24;
    padding: 0 1 0 0;
    margin: 0;
}

/* For rows with fewer checkboxes, make them wider */
.checkbox-row-1 Checkbox {
    width: 100%;
}

.checkbox-row-2 Checkbox {
    width: 50%;
}

.checkbox-row-3 Checkbox {
    width: 33%;
}

/* Updated styling for the checkboxes container to ensure it takes remaining space */
#checkboxes-container {
    width: 100%;
    height: 1fr; /* Take all remaining vertical space */
    overflow: auto;
    padding: 1;
    border: solid darkslateblue;
}

/* Styling for the model selection container */
#model-select-container {
    width: 100%;
    height: auto; /* Allow height to be determined by content */
    padding: 1;
    border: solid darkslateblue;
    background: #21222c;
}

/* Ensure the Select widget itself takes full width */
#model_select {
    width: 100%;
}

.settings-heading {
    text-style: bold;
    color: #8be9fd;
    margin: 0;
    padding: 0 0 1 1;
}

#settings-main {
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
}
